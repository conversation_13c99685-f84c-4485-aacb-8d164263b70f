// components/DeepInfoButton.jsx
import React, { useState, useEffect } from 'react';
import { <PERSON>Zap, FiLoader, FiInfo, FiStar, FiLock } from 'react-icons/fi';
import { useAuth } from '../../../../../context/AuthContext';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001';

const DeepInfoButton = ({ 
  mindMapData, 
  originalContext, 
  language = 'English',
  onEnhancementComplete,
  disabled = false 
}) => {
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [deepInfoStatus, setDeepInfoStatus] = useState(null);
  const [error, setError] = useState('');
  const { isAuthenticated, currentUser, token } = useAuth();

  // Fetch Deep Information status on component mount
  useEffect(() => {
    if (isAuthenticated && token) {
      fetchDeepInfoStatus();
    }
  }, [isAuthenticated, token]);

  const fetchDeepInfoStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/users/me/deep-info-status`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setDeepInfoStatus(response.data);
    } catch (error) {
      console.error('Error fetching Deep Info status:', error);
    }
  };

  const handleEnhanceWithDeepInfo = async () => {
    if (!isAuthenticated) {
      setError('Please sign in to use Deep Information feature.');
      return;
    }

    if (!mindMapData || !originalContext) {
      setError('Mind map data and original context are required.');
      return;
    }

    if (deepInfoStatus?.isLimitReached) {
      setError('You have reached your Deep Information limit. Please upgrade to Pro for unlimited access.');
      return;
    }

    setIsEnhancing(true);
    setError('');

    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/internal/mindmap/enhance-deep-info`,
        {
          mindMapData,
          originalContext,
          language
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data.success) {
        // Update status after successful enhancement
        await fetchDeepInfoStatus();
        
        // Call the callback with enhanced data
        if (onEnhancementComplete) {
          onEnhancementComplete(response.data.enhancedMindMapData);
        }
      }
    } catch (error) {
      console.error('Error enhancing mindmap:', error);
      
      if (error.response?.status === 403) {
        setError(error.response.data.message || 'Deep Information limit reached.');
        await fetchDeepInfoStatus(); // Refresh status
      } else if (error.response?.status === 401) {
        setError('Authentication failed. Please sign in again.');
      } else {
        setError(error.response?.data?.error || 'Failed to enhance mindmap with deep information.');
      }
    } finally {
      setIsEnhancing(false);
    }
  };

  const isPro = currentUser?.subscription?.planName === 'Pro';
  const isLimitReached = deepInfoStatus?.isLimitReached;
  const remainingUses = deepInfoStatus?.remainingUses;

  const getButtonText = () => {
    if (isEnhancing) return 'Enhancing...';
    if (isLimitReached) return 'Limit Reached';
    return 'Deep Information';
  };

  const getButtonIcon = () => {
    if (isEnhancing) return <FiLoader className="animate-spin" />;
    if (isLimitReached) return <FiLock />;
    return <FiZap />;
  };

  const getStatusText = () => {
    if (!deepInfoStatus) return '';
    if (isPro) return 'Unlimited access';
    if (isLimitReached) return 'Limit reached - Upgrade to Pro';
    return `${remainingUses} uses remaining`;
  };

  const getStatusColor = () => {
    if (isPro) return 'text-green-400';
    if (isLimitReached) return 'text-red-400';
    if (remainingUses === 1) return 'text-yellow-400';
    return 'text-blue-400';
  };

  return (
    <div className="flex flex-col items-center space-y-2">
      <button
        onClick={handleEnhanceWithDeepInfo}
        disabled={disabled || isEnhancing || isLimitReached || !isAuthenticated}
        className={`
          flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold text-sm
          transition-all duration-300 transform hover:scale-105
          ${isLimitReached || !isAuthenticated
            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
            : isPro
            ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-purple-500/25'
            : 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white hover:from-blue-700 hover:to-cyan-700 shadow-lg hover:shadow-blue-500/25'
          }
          ${isEnhancing ? 'animate-pulse' : ''}
        `}
        title={isLimitReached ? 'Upgrade to Pro for unlimited Deep Information access' : 'Enhance mindmap with AI-generated detailed information'}
      >
        {getButtonIcon()}
        <span>{getButtonText()}</span>
        {isPro && <FiStar className="text-yellow-400" />}
      </button>

      {/* Status indicator */}
      {deepInfoStatus && (
        <div className={`flex items-center space-x-1 text-xs ${getStatusColor()}`}>
          <FiInfo size={12} />
          <span>{getStatusText()}</span>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="text-red-400 text-xs text-center max-w-xs">
          {error}
        </div>
      )}

      {/* Feature description */}
      <div className="text-gray-400 text-xs text-center max-w-xs">
        Add detailed descriptions, sub-topics, and contextual insights to your mindmap
      </div>
    </div>
  );
};

export default DeepInfoButton;
