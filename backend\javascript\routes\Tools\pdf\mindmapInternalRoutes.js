// node_gemini_summarizer/routes/Tools/pdf/mindmapInternalRoutes.js
import express from 'express';
import { generateMindmapStructure } from '../../../controllers/tools/Pdf/mindmapInternalController.js';
import { enhanceMindMapDeepInfo } from '../../../controllers/tools/Pdf/deepInfoController.js';
import { protect } from '../../../middleware/authMiddleware.js';

const router = express.Router();

// This endpoint will be called by the Python service
router.post('/generate-from-summary', generateMindmapStructure);

// Deep Information enhancement endpoint (protected)
router.post('/enhance-deep-info', protect, enhanceMindMapDeepInfo);

export default router;