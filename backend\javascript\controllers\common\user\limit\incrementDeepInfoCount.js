// controllers/common/user/limit/incrementDeepInfoCount.js
import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    getLimit,
} from './planConfig.js';

/**
 * @desc    Increment Deep Information usage count for applicable plans
 * @route   POST /api/users/me/increment-deep-info-count
 * @access  Private
 */
export const incrementDeepInfoCount = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        user = ensureSubscription(user);

        let message = 'User is not on a plan that tracks Deep Information usage, count not incremented.';
        let countIncremented = false;
        let limitReached = false;

        if (user.subscription.planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const limit = getLimit(user.subscription.planName, 'deepInfo');
            if (user.subscription.freeTierDeepInfoCount >= limit) {
                limitReached = true;
                message = `Starter plan Deep Information limit of ${limit} uses reached. Please upgrade to Pro for unlimited access.`;
            } else {
                user.subscription.freeTierDeepInfoCount = (user.subscription.freeTierDeepInfoCount || 0) + 1;
                message = 'Starter plan Deep Information count incremented successfully.';
                countIncremented = true;
            }
        } else if (user.subscription.planName === PRO_PLAN_NAME_BACKEND) {
            // Pro users have unlimited access, so we still increment for tracking but no limit check
            user.subscription.proTierDeepInfoCount = (user.subscription.proTierDeepInfoCount || 0) + 1;
            message = 'Pro plan Deep Information count incremented successfully (unlimited access).';
            countIncremented = true;
        }

        if (limitReached) {
            return res.status(403).json({ 
                message,
                remainingUses: 0,
                isLimitReached: true
            });
        }

        if (countIncremented) {
            await user.save();
        }

        // Calculate remaining uses for response
        let remainingUses = Infinity;
        if (user.subscription.planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const limit = getLimit(user.subscription.planName, 'deepInfo');
            remainingUses = Math.max(0, limit - (user.subscription.freeTierDeepInfoCount || 0));
        }

        res.json({
            message: message,
            subscription: user.subscription,
            remainingUses: remainingUses,
            isLimitReached: false
        });

    } catch (error) {
        console.error('Increment Deep Information Count Error:', error);
        res.status(500).json({ message: 'Server error while incrementing Deep Information count.' });
    }
};

/**
 * @desc    Check Deep Information usage status without incrementing
 * @route   GET /api/users/me/deep-info-status
 * @access  Private
 */
export const getDeepInfoStatus = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        user = ensureSubscription(user);

        let remainingUses = Infinity;
        let isLimitReached = false;
        let currentCount = 0;
        let limit = Infinity;

        if (user.subscription.planName === FREE_TIER_PLAN_NAME_BACKEND) {
            limit = getLimit(user.subscription.planName, 'deepInfo');
            currentCount = user.subscription.freeTierDeepInfoCount || 0;
            remainingUses = Math.max(0, limit - currentCount);
            isLimitReached = currentCount >= limit;
        } else if (user.subscription.planName === PRO_PLAN_NAME_BACKEND) {
            currentCount = user.subscription.proTierDeepInfoCount || 0;
            // Pro users have unlimited access
            remainingUses = Infinity;
            isLimitReached = false;
        }

        res.json({
            planName: user.subscription.planName,
            currentCount,
            limit,
            remainingUses,
            isLimitReached,
            hasUnlimitedAccess: user.subscription.planName === PRO_PLAN_NAME_BACKEND
        });

    } catch (error) {
        console.error('Get Deep Information Status Error:', error);
        res.status(500).json({ message: 'Server error while checking Deep Information status.' });
    }
};
