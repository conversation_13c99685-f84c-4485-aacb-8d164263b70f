# python_pdf_processor/app/routes/pdf_processing_routes.py
import os
import tempfile
import json 
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from app.services.pdf.pdf_utils import extract_text_from_pdf
from app.services.pdf.mindmap_interaction import get_mindmap_from_node
from app.utils.caching import get_pdf_hash 
from app.config import Config

pdf_processing_bp = Blueprint('pdf_processing_bp', __name__)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

@pdf_processing_bp.route('/process-pdf-for-mindmap', methods=['POST'])
def process_pdf_for_mindmap():
    current_app.logger.info("<<<<< Request received at /api/pdf/process-pdf-for-mindmap (Python) >>>>>")
    if 'pdfFile' not in request.files:
        current_app.logger.warn("No 'pdfFile' part in the request.")
        return jsonify({"error": "No PDF file part in the request.", "details": "Please include a file with the key 'pdfFile'."}), 400
    
    file = request.files['pdfFile']
    
    # --- NEW: Get the language from the form data ---
    language = request.form.get('language', 'English') # Default to English if not provided
    current_app.logger.info(f"Requested mind map language: {language}")

    if file.filename == '':
        current_app.logger.warn("No file selected for upload.")
        return jsonify({"error": "No file selected.", "details": "The filename was empty."}), 400

    if file and allowed_file(file.filename):
        original_filename = secure_filename(file.filename)
        current_app.logger.info(f"Processing PDF: {original_filename} for mindmap generation via Node.js")
        
        extracted_text = None
        tmp_pdf_path = None

        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf", dir=Config.UPLOADS_FOLDER) as tmp_pdf:
                file.save(tmp_pdf.name)
                tmp_pdf_path = tmp_pdf.name
            current_app.logger.debug(f"PDF saved temporarily to: {tmp_pdf_path}")

            extracted_text = extract_text_from_pdf(tmp_pdf_path)
            if not extracted_text or not extracted_text.strip():
                current_app.logger.warn(f"No text could be extracted from {original_filename}, or text is empty.")
                if extracted_text is None:
                     return jsonify({"error": "Text extraction failed.", "details": "Could not extract any text content from the PDF."}), 400

            current_app.logger.info(f"Extracted text length: {len(extracted_text)} for {original_filename}")

            # --- MODIFIED: Pass the language to the Node.js interaction function ---
            mindmap_data = get_mindmap_from_node(extracted_text, original_filename, language)
            current_app.logger.info(f"Successfully obtained mindmap data for {original_filename} from Node.js service.")

            # Include original context for Deep Information feature
            response_data = mindmap_data.copy() if isinstance(mindmap_data, dict) else mindmap_data
            if isinstance(response_data, dict):
                response_data['originalContext'] = extracted_text[:5000]  # First 5000 chars for context

            return jsonify(response_data), 200

        except ConnectionError as e:
             current_app.logger.error(f"Node.js Connection Error for {original_filename}: {e}", exc_info=True)
             return jsonify({"error": "Could not connect to mindmap generation service.", "details": str(e)}), 503
        except TimeoutError as e:
             current_app.logger.error(f"Node.js Timeout Error for {original_filename}: {e}", exc_info=True)
             return jsonify({"error": "Mindmap generation service timed out.", "details": str(e)}), 504
        except ValueError as e:
            current_app.logger.error(f"Error from Node.js service for {original_filename}: {e}", exc_info=False)
            status_code = 502
            if "Node.js service error: 400" in str(e):
                status_code = 400
            return jsonify({"error": "Mindmap generation service failed.", "details_from_node": str(e)}), status_code
        except Exception as e:
            current_app.logger.error(f"General error processing PDF {original_filename} in Python: {e}", exc_info=True)
            return jsonify({"error": "Failed to process PDF or communicate with mindmap service.", "details": str(e)}), 500
        finally:
            if tmp_pdf_path and os.path.exists(tmp_pdf_path):
                try:
                    os.remove(tmp_pdf_path)
                    current_app.logger.debug(f"Temporary PDF file {tmp_pdf_path} removed.")
                except OSError as e_remove:
                    current_app.logger.error(f"Error removing temporary file {tmp_pdf_path}: {e_remove}")
    else:
        current_app.logger.warn(f"Invalid file type uploaded: {file.filename}")
        return jsonify({"error": "Invalid file type.", "details": f"Allowed types are: {', '.join(Config.ALLOWED_EXTENSIONS)}"}), 400