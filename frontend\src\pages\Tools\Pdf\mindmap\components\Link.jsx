import React, { useMemo } from 'react';
import * as d3 from 'd3';

// Define visual constants related to the link and the nodes it connects.
const CONNECTOR_THICKNESS = 4;
const OUTLINED_NODE_BORDER_WIDTH = 2;
const LINK_GLOW_BLUR = 8;

/**
 * A React component to render a single mind map link as a curved SVG path.
 * It calculates its own path data based on the source and target nodes.
 * @param {object} props
 * @param {object} props.linkData - The D3 link object, which contains `source` and `target` nodes.
 */
function Link({ linkData }) {
  // useMemo is crucial for performance here.
  // It prevents recalculating the complex SVG path string on every render,
  // unless the link's data has actually changed.
  const pathData = useMemo(() => {
    const { source, target } = linkData;

    // Get coordinates from the D3-processed data
    const sX = source.y;
    const sY = source.x;
    const tX = target.y;
    const tY = target.x;

    // Calculate the precise connection points on the edge of the node rectangles,
    // accounting for the border width of outlined nodes.
    const sourceOffset = source.isOutlinedOnly ? OUTLINED_NODE_BORDER_WIDTH / 2 : 0;
    const targetOffset = target.isOutlinedOnly ? OUTLINED_NODE_BORDER_WIDTH / 2 : 0;
    const sourceConnectX = sX + source.actualWidth / 2 + sourceOffset;
    const targetConnectX = tX - target.actualWidth / 2 - targetOffset;

    // The control point offset determines the "swoop" of the curve.
    const controlPointOffset = Math.abs(targetConnectX - sourceConnectX) * 0.6;

    // Use d3.path to build the SVG path string programmatically.
    const path = d3.path();
    path.moveTo(sourceConnectX, sY);
    path.bezierCurveTo(
      sourceConnectX + controlPointOffset, sY, // Control point 1 (near source)
      targetConnectX - controlPointOffset, tY, // Control point 2 (near target)
      targetConnectX, tY                      // End point
    );
    return path.toString();
  }, [linkData]); // The dependency array ensures this only runs when linkData changes.

  const linkColor = d3.color(linkData.target.color);
  const glowColor = linkColor.copy({ opacity: 0.4 });
  const linkId = `link-${linkData.source.id}-${linkData.target.id}`;

  return (
    <g>
      <defs>
        <filter id={`glow-${linkId}`} x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation={LINK_GLOW_BLUR} result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        <linearGradient id={`gradient-${linkId}`} x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor={linkData.source.color} stopOpacity="0.8" />
          <stop offset="100%" stopColor={linkData.target.color} stopOpacity="0.9" />
        </linearGradient>
      </defs>

      {/* Glow effect background */}
      <path
        d={pathData}
        fill="none"
        stroke={glowColor.toString()}
        strokeWidth={CONNECTOR_THICKNESS + 4}
        strokeOpacity={0.3}
        strokeLinecap="round"
        filter={`url(#glow-${linkId})`}
      />

      {/* Main link */}
      <path
        d={pathData}
        fill="none"
        stroke={`url(#gradient-${linkId})`}
        strokeWidth={CONNECTOR_THICKNESS}
        strokeOpacity={0.9}
        strokeLinecap="round"
        style={{
          transition: 'all 0.3s ease',
        }}
      />
    </g>
  );
}

export default Link;