// src/pages/Pdf/mindmap/MindMapDisplayPage.jsx
import React, { useEffect, useRef, useCallback, useState, useLayoutEffect } from "react";
import { useLocation, Link as RouterLink } from "react-router-dom";
import { FiAlertTriangle, FiArrowLeft } from "react-icons/fi";

// Import all child components
const HeaderBar = React.lazy(() => import("./components/HeaderBar"));
const MindMapCanvas = React.lazy(() => import("./components/MindMapCanvas"));
const PageStateDisplay = React.lazy(() =>
  import("./components/PageStateDisplay")
);
// Import hooks and helpers
import useMindMapData from "./hooks/useMindMapData";
import { debounce } from "./utils/helpers";

function MindMapDisplayPage() {
  const location = useLocation();

  // --- State managed by the Page Component ---
  const [mindMapData, setMindMapData] = useState(
    location.state?.mindMapData || null
  );
  const [originalContext, setOriginalContext] = useState(
    location.state?.originalContext || ''
  );
  const [pageLoading, setPageLoading] = useState(false);
  const [pageError, setPageError] = useState("");
  const [captureError, setCaptureError] = useState("");
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const d3Container = useRef(null);
  const svgRef = useRef(null);
  const gRef = useRef(null);

  // --- Effect for initial data loading ---
  useEffect(() => {
    setPageLoading(true);
    let data = location.state?.mindMapData;
    let context = location.state?.originalContext;

    // If no data in location state, try to get from sessionStorage
    if (!data) {
      const pathParts = location.pathname.split('/');
      const mindMapId = pathParts[pathParts.length - 1];
      const storedData = sessionStorage.getItem(`mindmap_${mindMapId}`);

      if (storedData) {
        try {
          const parsed = JSON.parse(storedData);
          data = parsed.mindMapData || parsed; // Handle both old and new formats
          context = parsed.originalContext || '';
        } catch (error) {
          console.error('Error parsing stored mindmap data:', error);
        }
      }
    }

    if (!data || !data.name) {
      setPageError("No mind map data provided. Please upload a PDF first.");
      setMindMapData(null);
    } else {
      setMindMapData(data);
      setOriginalContext(context || '');
      setPageError("");
    }
    setPageLoading(false);
  }, [location.state, location.pathname]);

  // --- Handler for mindmap updates from Deep Information enhancement ---
  const handleMindMapUpdate = useCallback((enhancedMindMapData) => {
    setMindMapData(enhancedMindMapData);
  }, []);

  // --- Effect for measuring container dimensions ---
  useLayoutEffect(() => {
    if (d3Container.current) {
      const updateDims = () => {
        if (d3Container.current) {
          setDimensions({
            width: d3Container.current.clientWidth,
            height: d3Container.current.clientHeight,
          });
        }
      };
      const debouncedUpdateDims = debounce(updateDims, 150);
      updateDims();
      window.addEventListener("resize", debouncedUpdateDims);
      return () => window.removeEventListener("resize", debouncedUpdateDims);
    }
  }, []);

  // --- Calling our custom hook to handle all D3 logic ---
  const { nodes, links, isD3Ready, zoomBehaviorRef, initialTransformRef } =
    useMindMapData({
      mindMapData,
      dimensions,
      svgRef,
      gRef,
    });

  // --- UI Rendering Logic ---
  const backToUploadButton = (
    <RouterLink
      to="/mindmap/upload"
      className="flex items-center px-6 py-2.5 bg-sky-600 text-white rounded-lg hover:bg-sky-500 transition-colors text-sm font-medium"
    >
      <FiArrowLeft className="mr-2 h-4 w-4" />
      Back to Upload
    </RouterLink>
  );

  return (
    <PageStateDisplay
      isLoading={pageLoading}
      error={pageError}
      hasData={!!mindMapData}
      loadingMessage="Loading Mind Map..."
      errorTitle="Error Displaying Mind Map"
      emptyTitle="No Mind Map data available."
      errorAction={backToUploadButton}
      emptyAction={backToUploadButton}
    >
      <div
        style={{
          width: "100vw",
          height: "100vh",
          display: "flex",
          flexDirection: "column",
          background: "#0c101d",
          color: "#E5E7EB",
        }}
      >
        <HeaderBar
          mindMapData={mindMapData}
          svgRef={svgRef}
          onDownloadError={setCaptureError}
          originalContext={originalContext}
          language="English"
          onMindMapUpdate={handleMindMapUpdate}
        />

        {captureError && (
          <div
            style={{
              padding: "8px 15px",
              background: "#7f1d1d",
              color: "#fecaca",
              textAlign: "center",
              fontSize: "0.85em",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <FiAlertTriangle
              style={{
                marginRight: "8px",
                width: "16px",
                height: "16px",
                flexShrink: 0,
              }}
            />
            <span>{captureError}</span>
          </div>
        )}

        <MindMapCanvas
          containerRef={d3Container}
          svgRef={svgRef}
          gRef={gRef}
          dimensions={dimensions}
          nodes={nodes}
          links={links}
          isD3Ready={isD3Ready}
          zoomBehaviorRef={zoomBehaviorRef}
          initialTransformRef={initialTransformRef}
        />
      </div>
    </PageStateDisplay>
  );
}

export default MindMapDisplayPage;