// common/user/limit/planConfig.js

// Plan Names
export const FREE_TIER_PLAN_NAME_BACKEND = 'Starter';
export const PRO_PLAN_NAME_BACKEND = 'Pro';

// --- STARTER PLAN LIMITS (Free Tier) ---
export const STARTER_BUSINESS_PLAN_LIMIT = 1;
export const STARTER_UPLOAD_LIMIT = 5;
export const STARTER_INVESTOR_PITCH_LIMIT = 3;
export const STARTER_BUSINESS_QA_LIMIT = 5;
export const STARTER_MESSAGE_LIMIT = 100; // Example message limit for Starter
export const STARTER_DEEP_INFO_LIMIT = 2; // Deep Information feature limit for Starter

// --- PRO PLAN LIMITS (Paid Tier) ---
// MODIFICATION: The Pro upload limit is now 25 instead of unlimited.
export const PRO_UPLOAD_LIMIT = 25;
export const PRO_BUSINESS_PLAN_LIMIT = 20;
export const PRO_INVESTOR_PITCH_LIMIT = 25;
export const PRO_BUSINESS_QA_LIMIT = 50;
export const PRO_MESSAGE_LIMIT = 300; // Example message limit for Pro
export const PRO_DEEP_INFO_LIMIT = Infinity; // Unlimited Deep Information for Pro users


// Helper function to check if a limit should be enforced for a plan
export const shouldEnforceLimit = (planName) => {
    // Limits are now enforced on BOTH plans.
    return planName === FREE_TIER_PLAN_NAME_BACKEND || planName === PRO_PLAN_NAME_BACKEND;
};

// Helper function to get the appropriate limit for a plan and feature
export const getLimit = (planName, feature) => {
    const limits = {
        [FREE_TIER_PLAN_NAME_BACKEND]: {
            businessPlan: STARTER_BUSINESS_PLAN_LIMIT,
            upload: STARTER_UPLOAD_LIMIT,
            investorPitch: STARTER_INVESTOR_PITCH_LIMIT,
            businessQA: STARTER_BUSINESS_QA_LIMIT,
            message: STARTER_MESSAGE_LIMIT,
            deepInfo: STARTER_DEEP_INFO_LIMIT
        },
        [PRO_PLAN_NAME_BACKEND]: {
            businessPlan: PRO_BUSINESS_PLAN_LIMIT,
            upload: PRO_UPLOAD_LIMIT, // This will now correctly return 25
            investorPitch: PRO_INVESTOR_PITCH_LIMIT,
            businessQA: PRO_BUSINESS_QA_LIMIT,
            message: PRO_MESSAGE_LIMIT,
            deepInfo: PRO_DEEP_INFO_LIMIT
        }
    };

    return limits[planName]?.[feature] || 0;
};

// Helper function to initialize subscription if it's missing
export const ensureSubscription = (user) => {
    if (!user.subscription) {
        user.subscription = {
            planName: FREE_TIER_PLAN_NAME_BACKEND,
            status: 'active',
            startDate: new Date(),
            freeTierUploadCount: 0,
            proTierUploadCount: 0,
            freeTierMessageCount: 0,
            proTierMessageCount: 0,
            freeTierBusinessPlanCount: 0,
            proTierBusinessPlanCount: 0,
            freeTierInvestorPitchCount: 0,
            proTierInvestorPitchCount: 0,
            freeTierBusinessQACount: 0,
            proTierBusinessQACount: 0,
            freeTierDeepInfoCount: 0,
            proTierDeepInfoCount: 0,
            businessPlanMonthlyReset: new Date(),
            investorPitchMonthlyReset: new Date(),
            businessQADailyReset: new Date(),
        };
    }
    return user;
};