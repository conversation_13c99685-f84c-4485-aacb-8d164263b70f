// utils/emojiSuggestions.js

/**
 * Emoji suggestion system for mindmap nodes based on content analysis
 */

// Comprehensive emoji mapping based on keywords and context
const EMOJI_MAPPINGS = {
  // Business & Finance
  business: ['💼', '🏢', '📊', '💰', '📈'],
  finance: ['💰', '💳', '📊', '💹', '🏦'],
  money: ['💰', '💵', '💸', '💳', '🪙'],
  profit: ['📈', '💰', '📊', '💹', '🎯'],
  revenue: ['💰', '📈', '💵', '📊', '💹'],
  investment: ['💹', '📈', '💰', '🎯', '📊'],
  market: ['📊', '📈', '🏪', '🛒', '💹'],
  sales: ['💰', '📈', '🛒', '💳', '🎯'],
  
  // Technology & Innovation
  technology: ['💻', '⚡', '🔧', '🚀', '💡'],
  innovation: ['💡', '🚀', '⚡', '🔬', '🎯'],
  software: ['💻', '📱', '⚙️', '🔧', '💾'],
  digital: ['💻', '📱', '🌐', '💾', '⚡'],
  ai: ['🤖', '🧠', '⚡', '💡', '🔮'],
  data: ['📊', '💾', '📈', '🗃️', '📋'],
  analytics: ['📊', '📈', '🔍', '📋', '💹'],
  
  // Education & Learning
  education: ['📚', '🎓', '📖', '✏️', '🧠'],
  learning: ['📚', '🧠', '💡', '📖', '🎯'],
  knowledge: ['🧠', '📚', '💡', '🔍', '📖'],
  research: ['🔬', '🔍', '📊', '📚', '💡'],
  study: ['📚', '📖', '✏️', '🧠', '📝'],
  
  // Health & Medical
  health: ['🏥', '💊', '🩺', '❤️', '🧬'],
  medical: ['🏥', '💊', '🩺', '🧬', '⚕️'],
  wellness: ['❤️', '🧘', '🌱', '💪', '🍎'],
  fitness: ['💪', '🏃', '🏋️', '⚡', '🎯'],
  
  // Environment & Nature
  environment: ['🌍', '🌱', '♻️', '🌳', '🌿'],
  nature: ['🌱', '🌳', '🌿', '🌍', '🦋'],
  sustainability: ['♻️', '🌱', '🌍', '🌿', '💚'],
  green: ['🌱', '🌿', '♻️', '💚', '🌳'],
  
  // Communication & Social
  communication: ['💬', '📞', '📧', '🗣️', '📢'],
  social: ['👥', '🤝', '💬', '🌐', '👫'],
  team: ['👥', '🤝', '⚡', '🎯', '🏆'],
  collaboration: ['🤝', '👥', '⚡', '🔗', '🎯'],
  
  // Goals & Strategy
  strategy: ['🎯', '📋', '🗺️', '⚡', '🧠'],
  goals: ['🎯', '🏆', '⭐', '📈', '🚀'],
  planning: ['📋', '🗺️', '📅', '🎯', '📊'],
  vision: ['👁️', '🔮', '⭐', '🎯', '🌟'],
  mission: ['🎯', '🚀', '⭐', '🗺️', '💫'],
  
  // Time & Process
  time: ['⏰', '📅', '⏳', '🕐', '⌛'],
  process: ['⚙️', '🔄', '📋', '🔧', '⚡'],
  workflow: ['🔄', '⚙️', '📋', '⚡', '🔗'],
  timeline: ['📅', '⏰', '📈', '🗓️', '⏳'],
  
  // Success & Achievement
  success: ['🏆', '⭐', '🎉', '💫', '🌟'],
  achievement: ['🏆', '🎯', '⭐', '🎉', '💪'],
  victory: ['🏆', '🎉', '⭐', '💫', '🥇'],
  excellence: ['⭐', '🏆', '💎', '🌟', '👑'],
  
  // Problem & Solution
  problem: ['❓', '⚠️', '🔍', '🧩', '💭'],
  solution: ['💡', '🔧', '✅', '🎯', '⚡'],
  challenge: ['⚡', '🎯', '💪', '🧗', '🔥'],
  opportunity: ['💡', '🚀', '⭐', '🎯', '💫'],
  
  // Default categories
  default: ['📌', '💡', '⭐', '🔹', '📋']
};

// Depth-based emoji suggestions
const DEPTH_EMOJIS = {
  0: ['🎯', '⭐', '💫', '🌟', '👑'], // Root node
  1: ['📌', '🔹', '💎', '⚡', '🎨'], // Main branches
  2: ['📍', '🔸', '💠', '⚪', '🔵'], // Sub-branches
  3: ['•', '▪️', '▫️', '🔹', '🔸']   // Leaf nodes
};

/**
 * Suggests an emoji based on the text content and node depth
 * @param {string} text - The text content of the node
 * @param {number} depth - The depth level of the node (0 = root)
 * @returns {string} - Suggested emoji
 */
export function suggestEmoji(text, depth = 0) {
  if (!text || typeof text !== 'string') {
    return DEPTH_EMOJIS[Math.min(depth, 3)][0];
  }
  
  const lowerText = text.toLowerCase();
  
  // Check for exact keyword matches first
  for (const [keyword, emojis] of Object.entries(EMOJI_MAPPINGS)) {
    if (keyword !== 'default' && lowerText.includes(keyword)) {
      return emojis[Math.floor(Math.random() * emojis.length)];
    }
  }
  
  // Check for partial matches or related terms
  const partialMatches = [];
  
  // Business terms
  if (lowerText.match(/\b(company|corporation|enterprise|startup|firm)\b/)) {
    partialMatches.push(...EMOJI_MAPPINGS.business);
  }
  
  // Technology terms
  if (lowerText.match(/\b(tech|digital|online|internet|web|app|system)\b/)) {
    partialMatches.push(...EMOJI_MAPPINGS.technology);
  }
  
  // Action terms
  if (lowerText.match(/\b(develop|create|build|design|implement)\b/)) {
    partialMatches.push('🔧', '⚡', '🛠️', '🎨', '💡');
  }
  
  // Growth terms
  if (lowerText.match(/\b(grow|increase|expand|scale|improve)\b/)) {
    partialMatches.push('📈', '🚀', '⬆️', '💹', '📊');
  }
  
  // People terms
  if (lowerText.match(/\b(people|users|customers|clients|team|staff)\b/)) {
    partialMatches.push('👥', '👤', '🤝', '👫', '👪');
  }
  
  // If we found partial matches, use them
  if (partialMatches.length > 0) {
    return partialMatches[Math.floor(Math.random() * partialMatches.length)];
  }
  
  // Fall back to depth-based emojis
  const depthEmojis = DEPTH_EMOJIS[Math.min(depth, 3)];
  return depthEmojis[Math.floor(Math.random() * depthEmojis.length)];
}

/**
 * Processes mindmap data to add emoji suggestions to nodes
 * @param {Object} mindMapData - The mindmap data structure
 * @returns {Object} - Enhanced mindmap data with emoji suggestions
 */
export function enhanceWithEmojis(mindMapData) {
  if (!mindMapData) return mindMapData;
  
  function processNode(node, depth = 0) {
    if (!node) return node;
    
    // Add suggested emoji if not already present
    if (!node.emoji && !node.name?.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu)) {
      node.suggestedEmoji = suggestEmoji(node.name, depth);
    }
    
    // Process children recursively
    if (node.children && Array.isArray(node.children)) {
      node.children = node.children.map(child => processNode(child, depth + 1));
    }
    
    return node;
  }
  
  return processNode({ ...mindMapData });
}

/**
 * Gets a random emoji from a specific category
 * @param {string} category - The category name
 * @returns {string} - Random emoji from the category
 */
export function getRandomEmojiFromCategory(category) {
  const emojis = EMOJI_MAPPINGS[category] || EMOJI_MAPPINGS.default;
  return emojis[Math.floor(Math.random() * emojis.length)];
}

/**
 * Gets all available emoji categories
 * @returns {Array} - Array of category names
 */
export function getEmojiCategories() {
  return Object.keys(EMOJI_MAPPINGS).filter(key => key !== 'default');
}
