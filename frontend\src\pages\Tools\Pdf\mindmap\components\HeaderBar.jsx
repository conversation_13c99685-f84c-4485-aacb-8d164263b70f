// src/components/HeaderBar.jsx

import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { FiArrowLeft } from 'react-icons/fi';
import DownloadButton from './DownloadButton';
import SaveMindMapButton from './SaveMindMapButton';
import DeepInfoButton from './DeepInfoButton';

/**
 * A reusable header bar component for the Mind Map display page.
 */
const HeaderBar = ({
  mindMapData,
  svgRef,
  onDownloadError,
  originalContext,
  language = 'English',
  onMindMapUpdate,
}) => {
  const mindMapName = mindMapData?.name || 'Mind Map';

  return (
    <header
      style={{
        padding: '10px 15px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        background: '#111827',
        borderBottom: '1px solid #1f2937',
        flexShrink: 0,
        gap: '10px',
      }}
    >
      <RouterLink
        to="/app/pdf/mindmap"
        className="flex items-center px-3 py-1.5 bg-slate-700 hover:bg-slate-600 rounded-md text-xs font-medium transition-colors"
        title="Upload New Mindmap"
      >
        <FiArrowLeft className="mr-1.5 h-3.5 w-3.5" />
        New
      </RouterLink>

      <h1
        style={{
          color: '#38BDF8',
          fontSize: '1.1em',
          fontWeight: '600',
          textAlign: 'center',
          flexGrow: 1,
          margin: '0 10px',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
        title={mindMapName}
      >
        {mindMapName}
      </h1>

      <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
        <DeepInfoButton
          mindMapData={mindMapData}
          originalContext={originalContext}
          language={language}
          onEnhancementComplete={onMindMapUpdate}
          disabled={!mindMapData}
        />

        <DownloadButton
          svgRef={svgRef}
          mindMapData={mindMapData}
          disabled={!mindMapData}
          onError={onDownloadError}
        />
      </div>
    </header>
  );
};

export default HeaderBar;