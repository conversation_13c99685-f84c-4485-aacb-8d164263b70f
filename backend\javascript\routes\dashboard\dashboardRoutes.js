// routes/dashboard/dashboardRoutes.js
import express from 'express';
import { getOverviewStats, getAllUsers, deleteUser } from '../../controllers/dashboard/dashboardController.js';
import { admin } from '../../middleware/adminMiddleware.js';

const router = express.Router();

// This line uses our simple, insecure 'admin' middleware for debugging.
// It does NOT use 'protect'.
router.use(admin);

// Route to get dashboard statistics
router.get('/stats', getOverviewStats);

// Route to get all users
router.get('/users', getAllUsers);

// Route to delete a user
router.delete('/users/:id', deleteUser);

export default router;