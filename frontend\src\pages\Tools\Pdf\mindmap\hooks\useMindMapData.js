// src/pages/Pdf/mindmap/hooks/useMindMapData.js

import { useState, useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { enhanceWithEmojis } from '../utils/emojiSuggestions';
import { estimateTextWidth } from '../utils/helpers';

export default function useMindMapData({ mindMapData, dimensions, svgRef, gRef }) {
  // State and Refs managed by the hook
  const [nodes, setNodes] = useState([]);
  const [links, setLinks] = useState([]);
  const [isD3Ready, setIsD3Ready] = useState(false);
  const zoomBehaviorRef = useRef(null);
  const initialTransformRef = useRef(d3.zoomIdentity);

  // The main D3 processing effect, now encapsulated in the hook
  useEffect(() => {
    // Guard clause: ensure we have data and a canvas to draw on.
    if (mindMapData && mindMapData.name && dimensions.width > 0 && svgRef.current && gRef.current) {
      // Enhance mindmap data with emoji suggestions
      const enhancedMindMapData = enhanceWithEmojis(mindMapData);
      const { width, height } = dimensions;
      const margin = { top: 30, right: 60, bottom: 30, left: 60 };
      const nodeHeight = 28;
      const outlinedNodeBorderWidth = 2;

      const root = d3.hierarchy(enhancedMindMapData);
      let nodeCounter = 0;

      function assignVisualProperties(node, parentBranchColor, depth1Index) {
        node.id = `node-${nodeCounter++}`;
        const nodeName = node.data.name || "";
        let currentFontSizePx = 12, fontWeight = "400";
        node.isOutlinedOnly = false; 
        const baseBranchColors = [
          "#ec4899", // Pink
          "#3b82f6", // Blue
          "#8b5cf6", // Purple
          "#10b981", // Green
          "#f59e0b", // Orange
          "#ef4444", // Red
          "#06b6d4", // Cyan
          "#84cc16", // Lime
          "#f97316", // Orange-red
          "#8b5cf6", // Violet
          "#14b8a6", // Teal
          "#f59e0b"  // Amber
        ];
        const rootColor = "#64748b";
        if (node.depth === 0) {
            currentFontSizePx = 14; fontWeight = "600"; node.branchColor = rootColor;
        } else if (node.depth === 1) {
            currentFontSizePx = 13; fontWeight = "500"; node.branchColor = baseBranchColors[depth1Index % baseBranchColors.length];
        } else { 
            currentFontSizePx = 11; node.branchColor = parentBranchColor; node.isOutlinedOnly = true;
        }
        node.color = node.branchColor; 
        const fontStyle = `${fontWeight} ${currentFontSizePx}px 'Inter', sans-serif`;
        const estimatedTextW = estimateTextWidth(nodeName, fontStyle);
        const nodePaddingHorizontal = 12;
        const effectivePadding = node.isOutlinedOnly ? nodePaddingHorizontal + outlinedNodeBorderWidth : nodePaddingHorizontal;
        node.actualWidth = Math.max(effectivePadding * 3, estimatedTextW + 2 * effectivePadding);
        node.actualHeight = nodeHeight;
        node.fontStyle = fontStyle;
        const availableTextSpace = node.actualWidth - (2 * effectivePadding);
        let maxChars = nodeName.length;
        if (estimatedTextW > availableTextSpace && availableTextSpace > 0) {
            const avgCharWidth = estimatedTextW / nodeName.length || (currentFontSizePx * 0.55); 
            maxChars = Math.floor(availableTextSpace / avgCharWidth) - 1; 
            if (maxChars < 3) maxChars = 3; 
        }
        node.maxDisplayChars = maxChars;
        if (node.children) {
            node.children.forEach((child, i) => {
                assignVisualProperties(child, node.branchColor, (node.depth === 0 ? i : depth1Index));
            });
        }
      }
      assignVisualProperties(root, null, 0);

      const treeLayout = d3.tree().nodeSize([nodeHeight + 35, 480]).separation((a, b) => (a.parent === b.parent ? 1.0 : 1.2)); 
      treeLayout(root);

      const additionalRootMarginX = 70; 
      root.descendants().forEach(dNode => { if (dNode.depth > 0) dNode.y += additionalRootMarginX; });
      
      let minX_coord = Infinity, maxX_coord = -Infinity, minY_coord = Infinity, maxY_coord = -Infinity;
      root.each(dNode => {
        const xPos = dNode.y; const yPos = dNode.x;
        const halfWidth = dNode.actualWidth / 2 + (dNode.isOutlinedOnly ? outlinedNodeBorderWidth / 2 : 0);
        const halfHeight = dNode.actualHeight / 2 + (dNode.isOutlinedOnly ? outlinedNodeBorderWidth / 2 : 0);
        if (xPos - halfWidth < minX_coord) minX_coord = xPos - halfWidth;
        if (xPos + halfWidth > maxX_coord) maxX_coord = xPos + halfWidth;
        if (yPos - halfHeight < minY_coord) minY_coord = yPos - halfHeight;
        if (yPos + halfHeight > maxY_coord) maxY_coord = yPos + halfHeight;
      });

      const treeWidthActual = maxX_coord - minX_coord;
      const treeHeightActual = maxY_coord - minY_coord;
      
      const newZoomBehavior = d3.zoom().scaleExtent([0.1, 3]) 
        .on("zoom", (event) => {
          if (gRef.current) d3.select(gRef.current).attr("transform", event.transform);
          if (svgRef.current) d3.select(svgRef.current).style("cursor", "grabbing");
        })
        .on("end", () => { if (svgRef.current) d3.select(svgRef.current).style("cursor", "grab"); });
      
      d3.select(svgRef.current).call(newZoomBehavior);
      zoomBehaviorRef.current = newZoomBehavior; 

      const initialScale = Math.min(
        (width - margin.left - margin.right) / Math.max(treeWidthActual, width * 0.7),
        (height - margin.top - margin.bottom) / Math.max(treeHeightActual, height * 0.7),
        1.0 
      );
      const clampedInitialScale = Math.max(0.15, Math.min(initialScale, 1.0)); 
      const initialTranslateX = (width / 2) - ((minX_coord + treeWidthActual / 2) * clampedInitialScale);
      const initialTranslateY = (height / 2) - ((minY_coord + treeHeightActual / 2) * clampedInitialScale);
      
      initialTransformRef.current = d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(clampedInitialScale);
      d3.select(svgRef.current).call(newZoomBehavior.transform, initialTransformRef.current);

      setNodes(root.descendants());
      setLinks(root.links());
      setIsD3Ready(true);
    } else {
      // If there's no data, ensure we reset the state
      setNodes([]);
      setLinks([]);
      setIsD3Ready(false);
    }
  }, [mindMapData, dimensions, svgRef, gRef]); // Dependencies of the hook

  // Return all the managed state and refs to the component
  return { nodes, links, isD3Ready, zoomBehaviorRef, initialTransformRef };
}