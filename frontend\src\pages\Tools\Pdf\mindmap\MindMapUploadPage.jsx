// src/pages/Pdf/mindmap/MindMapUploadPage.jsx
import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import OptimizedPdfUpload from '../../../../components/Tools/Pdf/mindmap/OptimizedPdfUpload';
import { useAuth } from '../../../../context/AuthContext';
import { useToast } from '../../../../components/Tools/Pdf/mindmap/hooks/useToast';
import { FiLoader, FiAlertTriangle } from 'react-icons/fi';

import UploadLimit from '../../../../common/messages/UploadLimit';

const PYTHON_SERVICE_URL = 'http://localhost:5001';
const FREE_TIER_PLAN_NAME = 'Starter';
const FREE_TIER_UPLOAD_LIMIT = 5;
const PRO_PLAN_NAME = 'Pro';
const PRO_TIER_UPLOAD_LIMIT = 25;

const ErrorIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-400 shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
);

const AuthRequiredPlaceholder = ({ openAuthPromptModal }) => (
  <div
    className="w-full border-2 border-dashed border-slate-700 rounded-xl p-12 text-center flex flex-col items-center justify-center bg-slate-900/20 cursor-pointer hover:border-sky-500/50 hover:bg-slate-900/40 transition-all duration-300"
    onClick={() => openAuthPromptModal ? openAuthPromptModal() : null}
  >
    <FiAlertTriangle className="w-10 h-10 text-yellow-400 mb-5" />
    <h3 className="font-semibold text-lg text-slate-100">Authentication Required</h3>
    <p className="text-slate-500 text-sm mt-2">Please sign in to generate a mind map.</p>
  </div>
);


function MindMapUploadPage({ openAuthPromptModal }) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { addToast } = useToast();
  
  const { isAuthenticated, isLoading: authIsLoading, currentUser, incrementUploadCount, fetchCurrentUser } = useAuth();

  useEffect(() => {
    if (authIsLoading) return;
    if (!isAuthenticated && openAuthPromptModal) openAuthPromptModal();
  }, [isAuthenticated, authIsLoading, openAuthPromptModal]);

  const { currentUploadCount, planUploadLimit, planNameForDisplay } = useMemo(() => {
    if (!currentUser?.subscription) return { currentUploadCount: 0, planUploadLimit: 0, planNameForDisplay: 'N/A' };
    const sub = currentUser.subscription;
    const currentPlanName = sub.planName;
    if (currentPlanName === FREE_TIER_PLAN_NAME) return { currentUploadCount: sub.freeTierUploadCount ?? 0, planUploadLimit: FREE_TIER_UPLOAD_LIMIT, planNameForDisplay: FREE_TIER_PLAN_NAME };
    if (currentPlanName === PRO_PLAN_NAME) return { currentUploadCount: sub.proTierUploadCount ?? 0, planUploadLimit: PRO_TIER_UPLOAD_LIMIT, planNameForDisplay: PRO_PLAN_NAME };
    return { currentUploadCount: 0, planUploadLimit: Infinity, planNameForDisplay: sub.planName || 'N/A' };
  }, [currentUser]);

  const isAllowedToUpload = useMemo(() => {
    if (!isAuthenticated || !currentUser?.subscription) return false;
    if (planUploadLimit === Infinity) return true;
    return currentUploadCount < planUploadLimit;
  }, [isAuthenticated, currentUser, currentUploadCount, planUploadLimit]);

  const handleMindMapUpload = async (file, language) => {
    setError('');

    if (!isAuthenticated) {
      openAuthPromptModal ? openAuthPromptModal() : navigate('/login');
      setError('Please sign in or create an account to generate mind maps.');
      return;
    }
    
    // This initial check is important to prevent users who are already at their limit from starting a new request.
    if (!isAllowedToUpload) {
      addToast('You have reached your upload limit.', 'error');
      return;
    }
    
    setIsProcessing(true);
    addToast(`Processing your PDF in ${language}, please wait...`, 'info', 10000);

    try {
      // --- THE USAGE COUNT IS NO LONGER INCREMENTED HERE ---
      // We will now increment it only after a successful response from the backend.

      const formData = new FormData();
      formData.append('pdfFile', file);
      formData.append('language', language);
      
      const pythonUploadUrl = `${PYTHON_SERVICE_URL}/api/pdf/process-pdf-for-mindmap`;
      const response = await axios.post(pythonUploadUrl, formData, { timeout: 180000 });

      // Check for a successful response from the mind map service
      if (response?.data?.name && !response.data.error) {
        
        // --- NEW POSITION: Increment usage count ONLY on success ---
        const incrementResult = await incrementUploadCount();
        
        // This is a rare but important edge case to handle.
        // If the mind map is generated but the database fails to record the usage,
        // we should still give the user their result but warn them.
        if (!incrementResult.success) {
            console.warn("CRITICAL: Mind map was generated, but failed to increment user's upload count.", incrementResult.error);
            addToast('Mind map created, but failed to update usage count.', 'warning');
        } else {
            // If the increment was successful, fetch the new user data to update the UI for the next visit.
            if (fetchCurrentUser) await fetchCurrentUser();
        }

        addToast('Mind map generated successfully!', 'success');

        const responseData = response.data;
        const mindMapData = { ...responseData };
        const originalContext = responseData.originalContext || '';

        // Remove originalContext from mindMapData to keep it clean
        delete mindMapData.originalContext;

        const mindMapId = Date.now().toString();

        sessionStorage.setItem(`mindmap_${mindMapId}`, JSON.stringify({ mindMapData, originalContext }));
        navigate(`/app/pdf/mindmap/display/${mindMapId}`, {
          state: {
            mindMapData,
            originalContext
          }
        });

      } else {
        // Handle cases where the backend returns a valid response but with an internal error message
        throw new Error(response?.data?.error || 'Received an unexpected response from the mind map service.');
      }
    } catch (err) {
      // This block will now catch network errors, timeouts, or errors thrown from the success check.
      // The upload count will NOT be incremented if an error occurs here, which is the correct behavior.
      console.error("[FRONTEND] ERROR during mind map generation process:", err);
      let errMsg = 'An unexpected error occurred.';
      if (err.code === 'ECONNABORTED') errMsg = 'The request timed out. The PDF might be too large or complex.';
      else if (err.response) errMsg = err.response.data?.error || `Service error: ${err.response.status}.`;
      else errMsg = err.message;
      
      setError(errMsg);
      addToast(errMsg, 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  if (authIsLoading) {
    return <div className="min-h-screen text-slate-100 flex flex-col items-center justify-center p-4"><FiLoader className="w-10 h-10 animate-spin text-sky-400 mb-4" />Loading...</div>;
  }
  
  const uploadDisabled = isProcessing || !isAuthenticated || !isAllowedToUpload;

  return (
    <div className="min-h-screen text-slate-100 flex flex-col items-center justify-center p-4 sm:p-8">
      <div className="p-6 sm:p-10 rounded-xl w-full max-w-lg md:max-w-xl space-y-8">
        <div className="text-center">
          <h1 className="text-3xl sm:text-4xl font-bold text-sky-400 mb-2">Create Your Mind Map</h1>
          <p className="text-slate-400 text-sm sm:text-base">Upload a PDF to automagically generate a mind map.</p>
        </div>

        {isAuthenticated ? (
          <>
            {!isAllowedToUpload && (
              <UploadLimit
                planName={planNameForDisplay}
                currentUploads={currentUploadCount}
                uploadLimit={planUploadLimit}
              />
            )}
            <OptimizedPdfUpload 
              onUpload={(file, lang) => handleMindMapUpload(file, lang)} 
              isLoading={isProcessing} 
              disabled={uploadDisabled} 
            />
          </>
        ) : (
          !authIsLoading && <AuthRequiredPlaceholder openAuthPromptModal={openAuthPromptModal} />
        )}

        {error && (
          <div className="bg-red-800/40 border border-red-700 text-red-300 px-4 py-3 rounded-lg flex items-start space-x-3 mt-6">
            <ErrorIcon />
            <div>
              <strong className="font-semibold block mb-1">Oops! An error occurred.</strong>
              <span className="block sm:inline text-sm">{error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default MindMapUploadPage;