// routes/userRoutes.js
import express from 'express';
import {
  incrementPdfUploadCount,
  incrementMessageCount,
  incrementBusinessPlanCount,
  incrementInvestorPitchCount,
  incrementBusinessQACount
} from '../../controllers/common/user/userActionsController.js';
import {
  incrementDeepInfoCount,
  getDeepInfoStatus
} from '../../controllers/common/user/limit/incrementDeepInfoCount.js';
import { protect } from '../../middleware/authMiddleware.js';

const router = express.Router();

// Route to increment the PDF upload count for the authenticated user
router.post('/me/increment-upload-count', protect, incrementPdfUploadCount);

// Route to increment the chat message count for the authenticated user
router.post('/me/increment-message-count', protect, incrementMessageCount);

// Route to increment the business plan generation count for the authenticated user
router.post('/me/increment-plan-count', protect, incrementBusinessPlanCount);

// Route to increment the investor pitch generation count for the authenticated user
router.post('/me/increment-pitch-count', protect, incrementInvestorPitchCount);

// Route to increment the business Q&A generation count for the authenticated user
router.post('/me/increment-qa-count', protect, incrementBusinessQACount);

// Route to increment the Deep Information usage count for the authenticated user
router.post('/me/increment-deep-info-count', protect, incrementDeepInfoCount);

// Route to get the Deep Information usage status for the authenticated user
router.get('/me/deep-info-status', protect, getDeepInfoStatus);

export default router;