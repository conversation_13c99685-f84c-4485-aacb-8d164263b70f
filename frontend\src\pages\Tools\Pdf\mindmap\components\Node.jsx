import React, { useState, useMemo } from 'react';
import * as d3 from 'd3';

// Define constants for node appearance
const NODE_CORNER_RADIUS = 8;
const OUTLINED_NODE_BORDER_WIDTH = 2;
const NODE_PADDING_HORIZONTAL = 16;
const EMOJI_SIZE = 20;
const EMOJI_OFFSET = 8;

/**
 * A React component to render a single mind map node.
 * It handles its own appearance, text truncation, and hover effects.
 * @param {object} props
 * @param {object} props.nodeData - The processed node data from d3.hierarchy.
 *        It should include x, y, actualWidth, actualHeight, color, fontStyle,
 *        isOutlinedOnly, maxDisplayChars, and data.name.
 */
function Node({ nodeData }) {
  const [isHovered, setIsHovered] = useState(false);

  // Memoize color calculations to avoid re-computing on every render
  const colors = useMemo(() => {
    const baseColor = d3.color(nodeData.color);
    const shadowColor = baseColor.copy({ opacity: 0.3 });

    if (nodeData.isOutlinedOnly) {
      return {
        fill: 'rgba(15, 23, 42, 0.85)', // Dark semi-transparent background
        stroke: baseColor.toString(),
        hoverFill: baseColor.copy({ opacity: 0.15 }).toString(),
        hoverStroke: baseColor.brighter(0.5).toString(),
        textColor: '#f8fafc',
        shadow: `0 4px 12px ${shadowColor.toString()}`,
      };
    } else {
      return {
        fill: baseColor.toString(),
        stroke: baseColor.brighter(0.3).toString(),
        hoverFill: baseColor.brighter(0.3).toString(),
        hoverStroke: baseColor.brighter(0.6).toString(),
        textColor: nodeData.depth === 0 ? '#ffffff' : '#f8fafc',
        shadow: `0 6px 20px ${shadowColor.toString()}`,
      };
    }
  }, [nodeData.color, nodeData.isOutlinedOnly, nodeData.depth]);

  // Extract emoji and text content
  const { emoji, text, displayText } = useMemo(() => {
    const name = nodeData.data.name || "";
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    const emojis = name.match(emojiRegex) || [];
    const textWithoutEmojis = name.replace(emojiRegex, '').trim();

    // Use the first emoji found, or a default based on node depth/content
    let selectedEmoji = emojis[0] || nodeData.data.emoji;

    // If no emoji exists, suggest one based on content or depth
    if (!selectedEmoji && nodeData.data.suggestedEmoji) {
      selectedEmoji = nodeData.data.suggestedEmoji;
    }

    const finalText = textWithoutEmojis || name;
    let truncatedText = finalText;

    if (finalText.length > nodeData.maxDisplayChars && nodeData.maxDisplayChars < finalText.length) {
      truncatedText = finalText.substring(0, nodeData.maxDisplayChars) + "…";
    }

    return {
      emoji: selectedEmoji,
      text: finalText,
      displayText: truncatedText
    };
  }, [nodeData.data.name, nodeData.data.emoji, nodeData.data.suggestedEmoji, nodeData.maxDisplayChars]);

  // Calculate text and emoji positions
  const emojiXPosition = -nodeData.actualWidth / 2 + (emoji ? EMOJI_OFFSET : 0);
  const textXPosition = -nodeData.actualWidth / 2 +
    (nodeData.isOutlinedOnly
      ? NODE_PADDING_HORIZONTAL + OUTLINED_NODE_BORDER_WIDTH
      : NODE_PADDING_HORIZONTAL) +
    (emoji ? EMOJI_SIZE + EMOJI_OFFSET : 0);

  return (
    <g
      transform={`translate(${nodeData.y}, ${nodeData.x})`}
      style={{ cursor: 'pointer', transition: 'all 0.3s ease' }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Drop shadow effect */}
      <defs>
        <filter id={`shadow-${nodeData.id}`} x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="4" stdDeviation={isHovered ? "8" : "4"} floodColor={colors.shadow} floodOpacity="0.3"/>
        </filter>
        {!nodeData.isOutlinedOnly && (
          <linearGradient id={`gradient-${nodeData.id}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={isHovered ? colors.hoverFill : colors.fill} />
            <stop offset="100%" stopColor={d3.color(isHovered ? colors.hoverFill : colors.fill).darker(0.2).toString()} />
          </linearGradient>
        )}
      </defs>

      <rect
        x={-nodeData.actualWidth / 2}
        y={-nodeData.actualHeight / 2}
        width={nodeData.actualWidth}
        height={nodeData.actualHeight}
        rx={NODE_CORNER_RADIUS}
        ry={NODE_CORNER_RADIUS}
        fill={nodeData.isOutlinedOnly ? (isHovered ? colors.hoverFill : colors.fill) : `url(#gradient-${nodeData.id})`}
        stroke={isHovered ? colors.hoverStroke : colors.stroke}
        strokeWidth={nodeData.isOutlinedOnly ? OUTLINED_NODE_BORDER_WIDTH : 1}
        filter={`url(#shadow-${nodeData.id})`}
        style={{
          transition: 'all 0.3s ease',
          transform: isHovered ? 'scale(1.05)' : 'scale(1)'
        }}
      />

      {/* Emoji */}
      {emoji && (
        <text
          x={emojiXPosition}
          y={0}
          dy="0.33em"
          textAnchor="start"
          style={{
            fontSize: `${EMOJI_SIZE}px`,
            pointerEvents: 'none',
            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
          }}
        >
          {emoji}
        </text>
      )}

      {/* Text content */}
      <text
        x={textXPosition}
        y={0}
        dy="0.33em"
        textAnchor="start"
        style={{
          font: nodeData.fontStyle,
          fill: colors.textColor,
          pointerEvents: 'none',
          textShadow: '0 1px 2px rgba(0,0,0,0.5)',
          fontWeight: nodeData.depth === 0 ? '600' : nodeData.depth === 1 ? '500' : '400'
        }}
      >
        {displayText}
      </text>

      {/* Enhanced tooltip */}
      <title>{nodeData.data.name}{nodeData.data.description ? `\n\n${nodeData.data.description}` : ''}</title>
    </g>
  );
}

export default Node;