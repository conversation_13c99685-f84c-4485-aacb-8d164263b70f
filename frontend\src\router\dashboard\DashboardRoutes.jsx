// src/router/dashboard/DashboardRoutes.jsx
import React, { Suspense } from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';

import DashboardLayout from '../../dashboard/components/DashboardLayout';
const OverviewPage = React.lazy(() => import('../../dashboard/pages/OverviewPage'));
const UserManagementPage = React.lazy(() => import('../../dashboard/pages/UserManagementPage'));

const GlobalSpinner = () => (
  <div className="fixed inset-0 flex items-center justify-center bg-slate-900 z-[100]">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-500"></div>
  </div>
);

const DashboardRoutes = () => {
  return (
    <DashboardLayout>
      <Suspense fallback={<GlobalSpinner />}>
        <Routes>
          <Route path="overview" element={<OverviewPage />} />
          <Route path="users" element={<UserManagementPage />} />
          {/* Default route for /admin -> redirects to /admin/overview */}
          <Route path="/" element={<Navigate to="overview" replace />} />
        </Routes>
      </Suspense>
    </DashboardLayout>
  );
};

export default DashboardRoutes;