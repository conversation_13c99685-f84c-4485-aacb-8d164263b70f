// controllers/tools/Pdf/deepInfoController.js
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";
import { incrementDeepInfoCount } from '../../common/user/limit/incrementDeepInfoCount.js';

// --- Gemini Configuration ---
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL_NAME = process.env.GEMINI_DEEP_INFO_MODEL_NAME || "gemini-1.5-flash-latest";

let genAIInstance;
let modelInstance;

// Initialize Gemini AI
function initializeGemini() {
    if (!GEMINI_API_KEY) {
        console.error("[Deep Info Controller] GEMINI_API_KEY environment variable is not set.");
        return null;
    }

    try {
        genAIInstance = new GoogleGenerativeAI(GEMINI_API_KEY);
        modelInstance = genAIInstance.getGenerativeModel({
            model: GEMINI_MODEL_NAME,
            safetySettings: [
                {
                    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                },
                {
                    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                },
                {
                    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                },
                {
                    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                },
            ],
        });
        console.log("[Deep Info Controller] Gemini AI initialized successfully.");
        return modelInstance;
    } catch (error) {
        console.error("[Deep Info Controller] Failed to initialize Gemini AI:", error);
        return null;
    }
}

function getGeminiModel() {
    if (!modelInstance) {
        return initializeGemini();
    }
    return modelInstance;
}

/**
 * Enhances a mindmap node with detailed AI-generated information
 * @param {Object} nodeData - The node data to enhance
 * @param {string} originalContext - The original document context
 * @param {string} language - The language for the enhancement
 * @returns {Object} - Enhanced node data
 */
async function enhanceNodeWithDeepInfo(nodeData, originalContext, language = 'English') {
    const model = getGeminiModel();
    if (!model) {
        throw new Error('AI Model for deep information enhancement is not available.');
    }

    const prompt = `
You are an AI assistant specialized in enhancing mind map nodes with detailed, contextual information.

Given the following mind map node and its original document context, enhance the node with:
1. A detailed description (2-3 sentences)
2. 2-3 relevant sub-headers/sub-topics
3. Additional context and explanations
4. A contextually appropriate emoji

Node to enhance: "${nodeData.name}"
Original document context: "${originalContext.substring(0, 2000)}..."

*** CRITICAL INSTRUCTION: The entire response, including all enhanced content, MUST be in the following language: ${language}. ***

Respond with a JSON object in this exact format:
{
  "enhancedName": "Enhanced node name with emoji",
  "description": "Detailed 2-3 sentence description",
  "subTopics": [
    "Sub-topic 1",
    "Sub-topic 2", 
    "Sub-topic 3"
  ],
  "additionalContext": "Additional explanatory context",
  "suggestedEmoji": "🎯",
  "keyInsights": [
    "Key insight 1",
    "Key insight 2"
  ]
}

Ensure the enhancement is relevant, informative, and adds meaningful value to the original node content.
`;

    try {
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();

        // Parse the JSON response
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('No valid JSON found in AI response');
        }

        const enhancedData = JSON.parse(jsonMatch[0]);
        
        // Validate the response structure
        if (!enhancedData.enhancedName || !enhancedData.description) {
            throw new Error('Invalid enhancement data structure');
        }

        return {
            ...nodeData,
            originalName: nodeData.name,
            name: enhancedData.enhancedName,
            description: enhancedData.description,
            subTopics: enhancedData.subTopics || [],
            additionalContext: enhancedData.additionalContext || '',
            suggestedEmoji: enhancedData.suggestedEmoji || '💡',
            keyInsights: enhancedData.keyInsights || [],
            isEnhanced: true,
            enhancedAt: new Date().toISOString()
        };

    } catch (error) {
        console.error('[Deep Info Controller] Error enhancing node:', error);
        throw new Error(`Failed to enhance node: ${error.message}`);
    }
}

/**
 * Recursively enhances all nodes in a mindmap structure
 * @param {Object} mindMapData - The complete mindmap data
 * @param {string} originalContext - The original document context
 * @param {string} language - The language for enhancement
 * @returns {Object} - Enhanced mindmap data
 */
async function enhanceMindMapWithDeepInfo(mindMapData, originalContext, language = 'English') {
    async function enhanceNode(node, depth = 0) {
        if (!node) return node;

        // Enhance the current node
        const enhancedNode = await enhanceNodeWithDeepInfo(node, originalContext, language);

        // Recursively enhance children
        if (node.children && Array.isArray(node.children)) {
            enhancedNode.children = await Promise.all(
                node.children.map(child => enhanceNode(child, depth + 1))
            );
        }

        return enhancedNode;
    }

    return await enhanceNode(mindMapData);
}

/**
 * @desc    Enhance mindmap with deep information using AI
 * @route   POST /api/mindmap/enhance-deep-info
 * @access  Private
 */
export const enhanceMindMapDeepInfo = async (req, res) => {
    try {
        const { mindMapData, originalContext, language = 'English' } = req.body;

        if (!mindMapData || !originalContext) {
            return res.status(400).json({ 
                error: 'Mind map data and original context are required.' 
            });
        }

        // Check and increment usage count
        const incrementResult = await incrementDeepInfoCount(req, res);
        
        // If the increment failed due to limits, the response is already sent
        if (res.headersSent) {
            return;
        }

        console.log('[Deep Info Controller] Enhancing mindmap with deep information...');

        // Enhance the mindmap with AI-generated deep information
        const enhancedMindMap = await enhanceMindMapWithDeepInfo(
            mindMapData, 
            originalContext, 
            language
        );

        console.log('[Deep Info Controller] Successfully enhanced mindmap with deep information.');

        res.json({
            success: true,
            enhancedMindMapData: enhancedMindMap,
            message: 'Mind map enhanced with deep information successfully.',
            enhancedAt: new Date().toISOString()
        });

    } catch (error) {
        console.error('[Deep Info Controller] Error enhancing mindmap:', error);
        
        if (error.message && (error.message.includes("API key not valid") || error.message.includes("PERMISSION_DENIED"))) {
            return res.status(401).json({ error: 'AI service authentication failed.', details: error.message });
        }
        if (error.message && error.message.includes("Rate limit exceeded")) {
            return res.status(429).json({ error: 'AI service rate limit exceeded.', details: error.message });
        }
        
        res.status(500).json({
            error: 'Failed to enhance mind map with deep information.',
            details: error.message
        });
    }
};
